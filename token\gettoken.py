from DrissionPage import Chromium, ChromiumOptions
import os
import re
import time

# 设置数据保存目录
DATA_DIR = r"D:\A_Driss_pageWork"
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)


def main():
    """主函数"""
    # 创建浏览器
    co = ChromiumOptions().set_user_data_path(os.path.join(DATA_DIR, 'browser_data_test'))
    browser = Chromium(co)
    page = browser.latest_tab
    
    try:
        # 访问网站
        page.get('https://targon.com/sign-in?mode=signup')
        time.sleep(50)
        turnstile_input = page.ele('@name=cf-turnstile-response')
        token_value = turnstile_input.attr('value')
        print(token_value)
        
            
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    finally:
        browser.quit()

if __name__ == '__main__':
    main()