from DrissionPage import Chromium, ChromiumOptions
import os
import re
import time
import shutil

# 设置数据保存目录
DATA_DIR = r"D:\A_Driss_pageWork"
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)


def main():
    """主函数"""
    # 创建浏览器
    co = ChromiumOptions().set_user_data_path(os.path.join(DATA_DIR, 'browser_data_test'))
    browser = Chromium(co)
    page = browser.latest_tab

    try:
        # 访问网站
        page.get('https://targon.com/sign-in?mode=signup')

        # 设置超时参数
        max_time = 30  # 最大等待时间30秒
        retry_interval = 2  # 每2秒重试一次
        start_time = time.time()

        print("开始获取token，最大等待30秒...")

        # 循环尝试获取token
        while time.time() - start_time < max_time:
            try:
                turnstile_input = page.ele('@name=cf-turnstile-response')
                if turnstile_input:
                    token_value = turnstile_input.attr('value')
                    if token_value and token_value.strip():  # 确保token不为空
                        print(f"成功获取token: {token_value}")
                        return token_value

                print(f"Token尚未生成，{retry_interval}秒后重试...")
                time.sleep(retry_interval)

            except Exception as e:
                print(f"获取token时出错: {str(e)}，{retry_interval}秒后重试...")
                time.sleep(retry_interval)

        # 超时未获取到token
        print("30秒内未能获取到token，程序退出")
        return None

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        return None
    finally:
        # 关闭浏览器
        browser.quit()

        # 删除浏览器数据文件夹
        try:
            browser_data_path = os.path.join(DATA_DIR, 'browser_data_test')
            if os.path.exists(browser_data_path):
                shutil.rmtree(browser_data_path)
                print(f"已删除浏览器数据文件夹: {browser_data_path}")
        except Exception as e:
            print(f"删除文件夹时出错: {str(e)}")

if __name__ == '__main__':
    main()