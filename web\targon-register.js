const axios = require('axios');
const fs = require('fs').promises;
const { generateTOTP } = require('./totp-generator');

class TargonRegister {
    constructor() {
        this.baseUrl = 'https://targon.com';
        this.emailDomain = 'umombiss.tk';
        this.emailApiBase = 'http://umombiss.tk'; // 临时邮箱API地址
        this.registeredAccounts = [];
        this.outputFile = 'registered_accounts.json';
        this.lockFile = 'registered_accounts.json.lock';
        this.cookies = ''; // 存储Cookie
        this.isLoggedIn = false; // 登录状态

        // 请求头配置
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        };
    }

    // 生成随机字符串
    generateRandomString(length, chars = 'abcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 生成随机邮箱
    generateRandomEmail() {
        const prefix = this.generateRandomString(8);
        return `${prefix}@${this.emailDomain}`;
    }

    // 生成随机密码
    generateRandomPassword() {
        return this.generateRandomString(8);
    }

    // 检查邮箱是否存在
    async checkEmail(email) {
        try {
            const params = {
                "0": {
                    "json": {
                        "email": email
                    }
                },
                "1": {
                    "json": {
                        "email": email
                    }
                }
            };

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.checkEmail,account.check2FA`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: this.headers
            });

            // console.log(`邮箱检查结果: ${email}`, response.data);
            return response.data;
        } catch (error) {
            console.error('检查邮箱失败:', error.message);
            throw error;
        }
    }

    // 获取 Turnstile Token (使用 Python 服务)
    async getTurnstileToken() {
        try {
            console.log('� 使用 Python 服务获取 Turnstile Token...');

            const pythonToken = await this.pythonTurnstile.getTurnstileToken();
            if (pythonToken) {
                console.log('✅ Python 服务成功获取 Token');
                return pythonToken;
            }

            console.log('❌ Python 服务获取 Token 失败');
            return null;
        } catch (error) {
            console.error('获取 Turnstile Token 失败:', error.message);
            return null;
        }
    }

    // 创建账户
    async createAccount(email, password, turnstileToken = null) {
        try {
            const data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password,
                        "turnstileToken": turnstileToken
                    }
                }
            };

            // 检查 turnstileToken 状态
            if (!turnstileToken) {
                console.log('⚠️ 警告: 没有提供 Turnstile Token，可能导致注册失败');
            }

            console.log('📤 发送创建账户请求:', {
                email: email,
                hasToken: !!turnstileToken,
                tokenPreview: turnstileToken ? turnstileToken.substring(0, 50) + '...' : 'null'
            });

            const response = await axios.post(`${this.baseUrl}/api/trpc/account.createAccount?batch=1`, data, {
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ 账户创建成功:', email);
            return response.data;
        } catch (error) {
            console.error('❌ 创建账户失败:', error.message);
            if (error.response) {
                console.error('状态码:', error.response.status);
                console.error('响应数据:', error.response.data);

                if (error.response.status === 400) {
                    console.error('💡 可能原因: 缺少有效的 Turnstile Token');
                }
            }
            throw error;
        }
    }

    // 获取邮件列表
    async getEmails(email) {
        try {
            const encodedEmail = encodeURIComponent(email);
            const response = await axios.get(`${this.emailApiBase}/api/mails/${encodedEmail}`);
            return response.data.mails || [];
        } catch (error) {
            console.error('获取邮件失败:', error.message);
            return [];
        }
    }

    // 等待验证邮件
    async waitForVerificationEmail(email, maxAttempts = 30, interval = 5000) {
        // console.log(`等待验证邮件: ${email}`);

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const emails = await this.getEmails(email);

                for (const mail of emails) {
                    if (mail.subject && mail.subject.toLowerCase().includes('verification') ||
                        mail.text && mail.text.includes('email-verification')) {

                        // 提取验证链接
                        const verificationLink = this.extractVerificationLink(mail.text || mail.html);
                        if (verificationLink) {
                            console.log(`✅ 找到验证邮件`);
                            return verificationLink;
                        }
                    }
                }

                if (attempt <= 3) {
                    console.log(`⏳ 等待验证邮件 (${attempt}/${maxAttempts})`);
                }
                await this.sleep(interval);
            } catch (error) {
                console.error(`检查邮件失败 (第${attempt}次):`, error.message);
                await this.sleep(interval);
            }
        }

        throw new Error(`等待验证邮件超时: ${email}`);
    }

    // 提取验证链接
    extractVerificationLink(content) {
        const regex = /https:\/\/targon\.com\/email-verification\/\?token=[a-zA-Z0-9]+/g;
        const match = content.match(regex);
        return match ? match[0] : null;
    }

    // 访问验证链接
    async verifyEmail(verificationLink) {
        try {
            const response = await axios.get(verificationLink, {
                headers: this.headers,
                maxRedirects: 5
            });

            // console.log(`邮箱验证成功: ${verificationLink}`);
            return true;
        } catch (error) {
            console.error('邮箱验证失败:', error.message);
            return false;
        }
    }

    // 登录账户
    async signIn(email, password) {
        try {
            const data = {
                "0": {
                    "json": {
                        "email": email,
                        "password": password
                    }
                }
            };

            const response = await axios.post(`${this.baseUrl}/api/trpc/account.signIn?batch=1`, data, {
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/json'
                }
            });

            // console.log(`登录结果: ${email}`, response.data);

            // 检查登录是否成功
            if (response.data && response.data[0] && response.data[0].result) {
                // console.log(`✅ 登录成功: ${email}`);
                this.isLoggedIn = true;

                // 保存Cookie信息
                if (response.headers['set-cookie']) {
                    this.cookies = response.headers['set-cookie'].join('; ');
                }

                return response.data;
            } else {
                throw new Error('登录响应格式异常');
            }
        } catch (error) {
            console.error('登录失败:', error.message);
            if (error.response && error.response.data) {
                console.error('登录错误详情:', error.response.data);
            }
            return null;
        }
    }

    // 获取用户信息和API Key (需要先登录)
    async getUserInfo() {
        try {
            if (!this.isLoggedIn) {
                throw new Error('用户未登录，无法获取用户信息');
            }

            const params = {
                "0": {"json": null, "meta": {"values": ["undefined"]}},
                "1": {"json": null, "meta": {"values": ["undefined"]}},
                "2": {"json": null, "meta": {"values": ["undefined"]}},
                "3": {"json": null, "meta": {"values": ["undefined"]}},
                "4": {"json": null, "meta": {"values": ["undefined"]}},
                "5": {"json": null, "meta": {"values": ["undefined"]}},
                "6": {"json": null, "meta": {"values": ["undefined"]}}
            };

            // 添加Cookie到请求头
            const requestHeaders = { ...this.headers };
            if (this.cookies) {
                requestHeaders['Cookie'] = this.cookies;
            }

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,keys.getApiKeys,notification.getNotifications`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: requestHeaders
            });
            
            // 提取API Key
            const apiKey = this.extractApiKey(response.data);
            return {
                userInfo: response.data,
                apiKey: apiKey
            };
        } catch (error) {
            console.error('获取用户信息失败:', error.message);
            return null;
        }
    }

    // 从响应中提取API Key
    extractApiKey(responseData) {
        try {
            // API Key在keys.getApiKeys的响应中，通常是第5个响应项（索引5）
            if (responseData && Array.isArray(responseData) && responseData.length > 5) {
                const keysResponse = responseData[5]; // keys.getApiKeys的响应

                if (keysResponse.result && keysResponse.result.data && keysResponse.result.data.json) {
                    const data = keysResponse.result.data.json;

                    // 处理data.json为数组的情况
                    if (Array.isArray(data) && data.length > 0) {
                        const firstKey = data[0];
                        if (firstKey && firstKey.key) {
                            console.log(`✅ 找到API Key: ${firstKey.key}`);
                            return firstKey.key;
                        }
                    }

                    // 处理其他可能的数据结构
                    if (data.key) {
                        console.log(`✅ 找到API Key: ${data.key}`);
                        return data.key;
                    }
                }
            }

            console.log('❌ 未找到API Key');
            return null;
        } catch (error) {
            console.error('提取API Key失败:', error.message);
            return null;
        }
    }

    // 获取2FA密钥
    async createTwoFactorURI() {
        try {
            if (!this.isLoggedIn) {
                throw new Error('用户未登录，无法创建2FA');
            }

            const params = {
                "0": {"json": null, "meta": {"values": ["undefined"]}}
            };

            // 添加Cookie到请求头
            const requestHeaders = { ...this.headers };
            if (this.cookies) {
                requestHeaders['Cookie'] = this.cookies;
            }

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.createTwoFactorURI`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: requestHeaders
            });

            // console.log('2FA密钥获取成功');

            if (response.data && Array.isArray(response.data) && response.data[0]) {
                const result = response.data[0].result;
                if (result && result.data && result.data.json) {
                    const twoFAData = result.data.json;
                    // console.log(`✅ 获取到2FA信息:`);
                    // console.log(`   URI: ${twoFAData.uri}`);
                    // console.log(`   Manual Code: ${twoFAData.manualCode}`);
                    // console.log(`   Two Factor Secret: ${twoFAData.twoFactorSecret}`);

                    return {
                        uri: twoFAData.uri,
                        manualCode: twoFAData.manualCode,
                        twoFactorSecret: twoFAData.twoFactorSecret
                    };
                }
            }

            throw new Error('2FA响应格式异常');
        } catch (error) {
            console.error('获取2FA密钥失败:', error.message);
            return null;
        }
    }

    // 启用2FA
    async enable2FA(otp, twoFactorSecret) {
        try {
            if (!this.isLoggedIn) {
                throw new Error('用户未登录，无法启用2FA');
            }

            const data = {
                "0": {
                    "json": {
                        "otp": otp,
                        "twoFactorSecret": twoFactorSecret
                    }
                }
            };

            // 添加Cookie到请求头
            const requestHeaders = {
                ...this.headers,
                'Content-Type': 'application/json'
            };
            if (this.cookies) {
                requestHeaders['Cookie'] = this.cookies;
            }

            const response = await axios.post(`${this.baseUrl}/api/trpc/account.enable2FA?batch=1`, data, {
                headers: requestHeaders
            });

            // console.log('2FA启用请求已发送');

            if (response.data && Array.isArray(response.data) && response.data[0]) {
                const result = response.data[0].result;
                if (result && result.data) {
                    // console.log(`✅ 2FA启用成功`);
                    return true;
                }
            }

            throw new Error('2FA启用响应格式异常');
        } catch (error) {
            console.error('启用2FA失败:', error.message);
            if (error.response && error.response.data) {
                console.error('2FA启用错误详情:', error.response.data);
            }
            return false;
        }
    }

    // 完整的2FA绑定流程
    async setup2FA() {
        try {
            // console.log('\n🔐 开始设置2FA...');

            // 1. 获取2FA密钥
            const twoFAData = await this.createTwoFactorURI();
            if (!twoFAData) {
                throw new Error('获取2FA密钥失败');
            }

            // 2. 生成TOTP验证码
            // console.log('🔢 生成TOTP验证码...');
            const otp = generateTOTP(twoFAData.manualCode);
            // console.log(`   生成的验证码: ${otp}`);

            // 3. 启用2FA
            // console.log('✅ 提交验证码启用2FA...');
            const success = await this.enable2FA(otp, twoFAData.twoFactorSecret);

            if (success) {
                // console.log('🎉 2FA设置成功！');
                return {
                    success: true,
                    manualCode: twoFAData.manualCode,
                    twoFactorSecret: twoFAData.twoFactorSecret,
                    uri: twoFAData.uri
                };
            } else {
                throw new Error('2FA启用失败');
            }
        } catch (error) {
            console.error('2FA设置失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 获取基础用户信息（仅用户数据，不包含API Key）
    async getBasicUserInfo() {
        try {
            if (!this.isLoggedIn) {
                throw new Error('用户未登录，无法获取用户信息');
            }

            // 添加Cookie到请求头
            const requestHeaders = { ...this.headers };
            if (this.cookies) {
                requestHeaders['Cookie'] = this.cookies;
            }

            const response = await axios.get(`${this.baseUrl}/api/trpc/account.getUser`, {
                params: {
                    batch: 1,
                    input: JSON.stringify({"0": {"json": null, "meta": {"values": ["undefined"]}}})
                },
                headers: requestHeaders
            });

            if (response.data && Array.isArray(response.data) && response.data[0]) {
                const result = response.data[0].result;
                if (result && result.data && result.data.json) {
                    const userInfo = result.data.json;
                    return userInfo;
                }
            }

            throw new Error('用户信息响应格式异常');
        } catch (error) {
            console.error('获取用户信息失败:', error.message);
            return null;
        }
    }

    // 获取完整用户信息（包含API Key）
    async getCompleteUserInfo() {
        try {
            if (!this.isLoggedIn) {
                throw new Error('用户未登录，无法获取用户信息');
            }

            // 构建批量请求参数
            const params = {
                "0": {"json": null, "meta": {"values": ["undefined"]}}, // keys.getApiKeys
                "1": {"json": {"days": 30, "limit": 3}}, // model.getPopularModels
                "2": {"json": null, "meta": {"values": ["undefined"]}}, // account.getUserBookmarks
                "3": {"json": null, "meta": {"values": ["undefined"]}}, // account.getUserInterest
                "4": {"json": null, "meta": {"values": ["undefined"]}}, // account.getUserSubscription
                "5": {"json": {"title": "Targon Starter"}}, // subscriptionPlan.getPlanDetails
                "6": {"json": {"email": ""}}, // account.check2FA
                "7": {"json": null, "meta": {"values": ["undefined"]}}, // account.getTaoPrice
                "8": {"json": null, "meta": {"values": ["undefined"]}}, // account.getAlphaPrice
                "9": {"json": null, "meta": {"values": ["undefined"]}} // account.getUser
            };

            // 添加Cookie到请求头
            const requestHeaders = { ...this.headers };
            if (this.cookies) {
                requestHeaders['Cookie'] = this.cookies;
            }

            const response = await axios.get(`${this.baseUrl}/api/trpc/keys.getApiKeys,model.getPopularModels,account.getUserBookmarks,account.getUserInterest,account.getUserSubscription,subscriptionPlan.getPlanDetails,account.check2FA,account.getTaoPrice,account.getAlphaPrice,account.getUser`, {
                params: {
                    batch: 1,
                    input: JSON.stringify(params)
                },
                headers: requestHeaders
            });

            if (response.data && Array.isArray(response.data)) {
                // 提取API Key (索引0)
                let apiKey = null;
                if (response.data[0] && response.data[0].result && response.data[0].result.data && response.data[0].result.data.json) {
                    const keysData = response.data[0].result.data.json;
                    if (Array.isArray(keysData) && keysData.length > 0 && keysData[0].key) {
                        apiKey = keysData[0].key;
                    }
                }

                // 提取用户信息 (索引9)
                let userInfo = null;
                if (response.data[9] && response.data[9].result && response.data[9].result.data && response.data[9].result.data.json) {
                    userInfo = response.data[9].result.data.json;
                }

                if (userInfo) {
                    // console.log(`✅ 完整用户信息获取成功:`);
                    // console.log(`   ID: ${userInfo.id}`);
                    // console.log(`   Email: ${userInfo.email}`);
                    // console.log(`   API Key: ${apiKey || '未找到'}`);
                    // console.log(`   Bought Credits: ${userInfo.boughtCredits}`);
                    // console.log(`   Plan Credits: ${userInfo.planCredits}`);
                    // console.log(`   Budget: ${userInfo.budget}`);
                    // console.log(`   Plan ID: ${userInfo.planId}`);

                    return {
                        ...userInfo,
                        apiKey: apiKey
                    };
                }
            }

            throw new Error('完整用户信息响应格式异常');
        } catch (error) {
            console.error('获取完整用户信息失败:', error.message);
            return null;
        }
    }

    // 睡眠函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 获取文件锁
    async acquireLock(maxWaitTime = 30000) {
        const startTime = Date.now();
        const lockId = `${process.pid}-${Date.now()}-${Math.random()}`;

        // 首先尝试清理过期锁
        await this.cleanupStaleLock();

        while (Date.now() - startTime < maxWaitTime) {
            try {
                // 尝试创建锁文件，如果文件已存在则会失败
                await fs.writeFile(this.lockFile, lockId, { flag: 'wx' });
                console.log(`🔒 获取文件锁成功: ${lockId}`);
                return lockId;
            } catch (error) {
                if (error.code === 'EEXIST') {
                    // 锁文件已存在，等待一下再重试
                    await this.sleep(100 + Math.random() * 200); // 随机等待100-300ms

                    // 每隔5秒尝试清理一次过期锁
                    if ((Date.now() - startTime) % 5000 < 300) {
                        await this.cleanupStaleLock();
                    }
                    continue;
                } else {
                    throw error;
                }
            }
        }

        throw new Error(`获取文件锁超时 (${maxWaitTime}ms)`);
    }

    // 释放文件锁
    async releaseLock(lockId) {
        try {
            // 验证锁文件内容是否匹配
            const currentLockId = await fs.readFile(this.lockFile, 'utf8');
            if (currentLockId === lockId) {
                await fs.unlink(this.lockFile);
                console.log(`🔓 释放文件锁成功: ${lockId}`);
            } else {
                console.warn(`⚠️ 锁文件内容不匹配，可能已被其他进程释放`);
            }
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error('释放文件锁失败:', error.message);
            }
        }
    }

    // 清理过期的锁文件 (如果锁文件超过5分钟没有更新，认为是僵尸锁)
    async cleanupStaleLock() {
        try {
            const stats = await fs.stat(this.lockFile);
            const lockAge = Date.now() - stats.mtime.getTime();

            if (lockAge > 5 * 60 * 1000) { // 5分钟
                console.warn('⚠️ 发现过期锁文件，正在清理...');
                await fs.unlink(this.lockFile);
                console.log('🧹 过期锁文件已清理');
            }
        } catch (error) {
            // 锁文件不存在或其他错误，忽略
        }
    }

    // 保存账户信息到JSON文件 (带文件锁保护)
    async saveAccountToFile(accountInfo) {
        let lockId = null;

        try {
            // 获取文件锁
            lockId = await this.acquireLock();

            let existingData = [];

            // 检查文件是否存在
            try {
                await fs.access(this.outputFile);
                // 文件存在，尝试读取
                try {
                    const fileContent = await fs.readFile(this.outputFile, 'utf8');
                    if (fileContent.trim()) {
                        existingData = JSON.parse(fileContent);
                        console.log(`📖 读取到 ${existingData.length} 个现有账户`);
                    } else {
                        console.log('📄 文件为空，初始化为空数组');
                    }
                } catch (readError) {
                    // 文件存在但读取失败，这是严重错误，不应该覆盖
                    console.error('❌ 严重错误：文件存在但无法读取，拒绝写入以防数据丢失');
                    console.error('读取错误详情:', readError.message);
                    throw new Error(`文件读取失败，拒绝写入以保护现有数据: ${readError.message}`);
                }
            } catch (accessError) {
                // 文件不存在或其他访问错误，都拒绝写入以保护数据
                if (accessError.code === 'ENOENT') {
                    console.error('❌ 严重错误：registered_accounts.json 文件不存在！');
                    console.error('为了保护数据安全，拒绝创建新文件');
                    console.error('请检查文件是否被意外删除或移动');
                    throw new Error('registered_accounts.json 文件不存在，拒绝创建新文件以防数据丢失');
                } else {
                    // 其他访问错误（权限、锁定等），不应该覆盖
                    console.error('❌ 严重错误：文件访问失败，拒绝写入以防数据丢失');
                    console.error('访问错误详情:', accessError.message);
                    throw new Error(`文件访问失败，拒绝写入以保护现有数据: ${accessError.message}`);
                }
            }

            // 创建备份（仅在特定条件下）
            // 只有当账户数量是100的倍数时才创建备份，避免过多备份文件
            if (existingData.length > 0 && existingData.length % 100 === 0) {
                const backupFile = `${this.outputFile}.backup.${Date.now()}`;
                try {
                    await fs.writeFile(backupFile, JSON.stringify(existingData, null, 2), 'utf8');
                    console.log(`💾 已创建定期备份: ${backupFile} (账户数: ${existingData.length})`);
                } catch (backupError) {
                    console.warn('⚠️ 备份创建失败，但继续执行:', backupError.message);
                }
            }

            // 添加新账户信息
            existingData.push({
                ...accountInfo,
                registeredAt: new Date().toISOString()
            });

            // 写入文件
            await fs.writeFile(this.outputFile, JSON.stringify(existingData, null, 2), 'utf8');
            console.log(`✅ 账户信息已保存，当前总数: ${existingData.length}`);

        } catch (error) {
            console.error('❌ 保存账户信息失败:', error.message);
            throw error; // 重新抛出错误，让调用者知道保存失败
        } finally {
            // 无论成功还是失败，都要释放文件锁
            if (lockId) {
                await this.releaseLock(lockId);
            }
        }
    }

    // 单个账户注册流程 (包含2FA) - 支持索引显示
    async registerSingleAccount(index = null, total = null) {
        const email = this.generateRandomEmail();
        const password = this.generateRandomPassword();

        const logPrefix = index ? `[${index}/${total}]` : '';
        console.log(`${logPrefix} 🚀 注册账户: ${email}`);

        try {
            // 1. 检查邮箱
            await this.checkEmail(email);

            // 2. 获取 Turnstile Token
            const turnstileToken = await this.getTurnstileToken();
            if (!turnstileToken) {
                console.log(`${logPrefix} ⚠️ 警告: 未获取到 Turnstile Token，可能导致注册失败`);
            }

            // 3. 创建账户
            await this.createAccount(email, password, turnstileToken);

            // 4. 等待验证邮件
            const verificationLink = await this.waitForVerificationEmail(email);

            // 5. 验证邮箱
            const verified = await this.verifyEmail(verificationLink);

            if (verified) {
                // 等待几秒让系统处理验证状态
                await this.sleep(3000);

                // 6. 登录账户
                const loginResult = await this.signIn(email, password);

                if (!loginResult) {
                    throw new Error('登录失败');
                }

                // 等待登录状态生效
                await this.sleep(2000);

                // 7. 获取用户信息和API Key
                const userInfoResult = await this.getCompleteUserInfo();
                let apiKey = null;

                if (userInfoResult && userInfoResult.apiKey) {
                    apiKey = userInfoResult.apiKey;
                } else {
                    // 重试一次
                    await this.sleep(2000);
                    const retryResult = await this.getCompleteUserInfo();
                    if (retryResult && retryResult.apiKey) {
                        apiKey = retryResult.apiKey;
                    }
                }

                // 8. 设置2FA并获取0.2美元奖励
                const twoFAResult = await this.setup2FA();
                let twoFactorData = null;
                let twoFactorEnabled = false;

                if (twoFAResult && twoFAResult.success) {
                    twoFactorEnabled = true;
                    twoFactorData = {
                        manualCode: twoFAResult.manualCode,
                        twoFactorSecret: twoFAResult.twoFactorSecret,
                        uri: twoFAResult.uri
                    };
                } else {
                    console.log(`${logPrefix} ⚠️ 2FA设置失败:`, twoFAResult?.error || '未知错误');
                }

                // 9. 获取最新的用户信息和余额
                await this.sleep(2000); // 等待余额更新
                const finalUserInfo = await this.getCompleteUserInfo();

                const accountInfo = {
                    email: email,
                    password: password,
                    verificationLink: verificationLink,
                    apiKey: apiKey,
                    status: 'verified',
                    loginStatus: true,
                    twoFactorEnabled: twoFactorEnabled,
                    twoFactorData: twoFactorData,
                    creditBalance: finalUserInfo ? {
                        boughtCredits: finalUserInfo.boughtCredits,
                        planCredits: finalUserInfo.planCredits,
                        totalCredits: (finalUserInfo.boughtCredits || 0) + (finalUserInfo.planCredits || 0)
                    } : null
                };

                // 10. 保存账户信息
                await this.saveAccountToFile(accountInfo);
                this.registeredAccounts.push(accountInfo);

                // 显示注册成功信息
                const creditTotal = (finalUserInfo?.boughtCredits || 0) + (finalUserInfo?.planCredits || 0);
                console.log(`${logPrefix} 🎉 注册成功! API: ${apiKey?.slice(-8) || 'N/A'} | 2FA: ${twoFactorEnabled ? '✅' : '❌'} | 余额: ${creditTotal/******** || 0}$`);
                return accountInfo;
            } else {
                throw new Error('邮箱验证失败');
            }

        } catch (error) {
            console.error(`${logPrefix} ❌ 注册失败: ${error.message}`);
            // 不保存失败的账户信息
            return null;
        }
    }

    // 批量注册 (串行)
    async batchRegister(count = 1, delay = 10000) {
        console.log(`开始批量注册 ${count} 个账户，间隔 ${delay/1000} 秒`);

        const results = [];

        for (let i = 1; i <= count; i++) {
            console.log(`\n=== 注册第 ${i}/${count} 个账户 ===`);

            const result = await this.registerSingleAccount();
            results.push(result);

            // 如果不是最后一个账户，等待指定时间
            if (i < count) {
                console.log(`等待 ${delay/1000} 秒后继续下一个账户...`);
                await this.sleep(delay);
            }
        }

        const successCount = results.filter(r => r !== null).length;
        console.log(`\n=== 批量注册完成 ===`);
        console.log(`成功: ${successCount}/${count}`);
        console.log(`失败: ${count - successCount}/${count}`);

        return results;
    }

    // 批量注册 (并行)
    async batchRegisterParallel(count = 1, maxConcurrent = 3) {
        console.log(`开始并行批量注册 ${count} 个账户，最大并发数: ${maxConcurrent}`);

        const results = [];
        const promises = [];

        // 创建注册任务
        for (let i = 1; i <= count; i++) {
            const promise = this.registerSingleAccount(i, count);
            promises.push(promise);

            // 控制并发数
            if (promises.length >= maxConcurrent || i === count) {
                const batchResults = await Promise.allSettled(promises);

                // 处理结果
                for (const result of batchResults) {
                    if (result.status === 'fulfilled') {
                        results.push(result.value);
                    } else {
                        console.error('注册任务失败:', result.reason?.message || result.reason);
                        results.push(null);
                    }
                }

                // 清空当前批次
                promises.length = 0;

                // 如果不是最后一批，稍微等待一下
                if (i < count) {
                    await this.sleep(1000);
                }
            }
        }

        const successCount = results.filter(r => r !== null).length;
        console.log(`\n=== 并行批量注册完成 ===`);
        console.log(`成功: ${successCount}/${count}`);
        console.log(`失败: ${count - successCount}/${count}`);

        return results;
    }




}

// 导出类
module.exports = TargonRegister;

// 如果直接运行此文件
if (require.main === module) {
    const register = new TargonRegister();
    
    // 从命令行参数获取注册数量
    const count = parseInt(process.argv[2]) || 1;
    const delay = parseInt(process.argv[3]) || 10000;
    
    register.batchRegister(count, delay)
        .then(() => {
            console.log('批量注册任务完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('批量注册任务失败:', error);
            process.exit(1);
        });
}
